export interface MentionUser {
  id: string;
  name: string;
  avatarUrl?: string;
  username?: string;
}

export interface ParsedMention {
  id: string;
  name: string;
  username?: string;
  startIndex: number;
  endIndex: number;
  displayText: string; // e.g., "@john_doe"
}

export interface MentionParseResult {
  text: string; // Original text
  mentions: ParsedMention[];
  mentionedUserIds: string[];
  displayText: string; // Text with mentions formatted for display
}

/**
 * Parse @mentions from text and extract mentioned users
 * Supports formats like @username, @"Full Name", @user_id
 */
export function parseMentions(
  text: string,
  availableUsers: MentionUser[] = []
): MentionParseResult {
  const mentions: ParsedMention[] = [];
  const mentionedUserIds: string[] = [];

  // Regex to match @mentions - supports @username, @"Full Name", or @user_id
  const mentionRegex = /@(?:"([^"]+)"|(\w+))/g;
  let match;

  while ((match = mentionRegex.exec(text)) !== null) {
    const fullMatch = match[0]; // Full @mention text
    const quotedName = match[1]; // Name in quotes
    const username = match[2]; // Username without quotes
    const startIndex = match.index;
    const endIndex = match.index + fullMatch.length;

    const searchTerm = quotedName || username;

    // Find matching user
    const matchedUser = availableUsers.find(
      (user) =>
        user.username?.toLowerCase() === searchTerm.toLowerCase() ||
        user.name.toLowerCase() === searchTerm.toLowerCase() ||
        user.id === searchTerm
    );

    if (matchedUser) {
      mentions.push({
        id: matchedUser.id,
        name: matchedUser.name,
        username: matchedUser.username,
        startIndex,
        endIndex,
        displayText: fullMatch,
      });

      if (!mentionedUserIds.includes(matchedUser.id)) {
        mentionedUserIds.push(matchedUser.id);
      }
    }
  }

  return {
    text,
    mentions,
    mentionedUserIds,
    displayText: text,
  };
}

/**
 * Replace @mentions in text with formatted display text
 */
export function formatMentionsForDisplay(
  text: string,
  mentions: ParsedMention[]
): string {
  if (mentions.length === 0) return text;

  let formattedText = text;
  let offset = 0;

  // Sort mentions by start index to process them in order
  const sortedMentions = [...mentions].sort(
    (a, b) => a.startIndex - b.startIndex
  );

  for (const mention of sortedMentions) {
    const start = mention.startIndex + offset;
    const end = mention.endIndex + offset;
    const displayName = `@${mention.username || mention.name}`;

    formattedText =
      formattedText.slice(0, start) + displayName + formattedText.slice(end);

    offset += displayName.length - (mention.endIndex - mention.startIndex);
  }

  return formattedText;
}

/**
 * Create mention data for storing in GetStream comment
 */
export function createMentionData(mentions: ParsedMention[]) {
  return {
    mentioned_users: mentions.map((mention) => ({
      id: mention.id,
      name: mention.name,
      username: mention.username,
    })),
    mention_count: mentions.length,
    has_mentions: mentions.length > 0,
  };
}

/**
 * Extract mentions from GetStream comment data
 */
export function extractMentionsFromComment(commentData: any): ParsedMention[] {
  if (!commentData?.data?.mentioned_users) return [];

  const mentionedUsers = commentData.data.mentioned_users;
  const text = commentData.data?.text || "";
  const mentions: ParsedMention[] = [];

  // Find mention positions in text
  mentionedUsers.forEach((user: any) => {
    const mentionPattern = new RegExp(
      `@(?:"${user.name}"|${user.username || user.name})`,
      "gi"
    );
    let match;

    while ((match = mentionPattern.exec(text)) !== null) {
      mentions.push({
        id: user.id,
        name: user.name,
        username: user.username,
        startIndex: match.index,
        endIndex: match.index + match[0].length,
        displayText: match[0],
      });
    }
  });

  return mentions;
}

/**
 * Search users for mention autocomplete
 */
export function searchUsersForMention(
  query: string,
  users: MentionUser[],
  limit: number = 10
): MentionUser[] {
  if (!query.trim()) return [];

  const searchTerm = query.toLowerCase();

  return users
    .filter(
      (user) =>
        user.name.toLowerCase().includes(searchTerm) ||
        user.username?.toLowerCase().includes(searchTerm)
    )
    .slice(0, limit)
    .sort((a, b) => {
      // Prioritize exact matches and username matches
      const aNameMatch = a.name.toLowerCase().startsWith(searchTerm);
      const bNameMatch = b.name.toLowerCase().startsWith(searchTerm);
      const aUsernameMatch = a.username?.toLowerCase().startsWith(searchTerm);
      const bUsernameMatch = b.username?.toLowerCase().startsWith(searchTerm);

      if (aUsernameMatch && !bUsernameMatch) return -1;
      if (!aUsernameMatch && bUsernameMatch) return 1;
      if (aNameMatch && !bNameMatch) return -1;
      if (!aNameMatch && bNameMatch) return 1;

      return a.name.localeCompare(b.name);
    });
}

/**
 * Get cursor position for mention insertion
 */
export function getMentionInsertPosition(
  text: string,
  cursorPosition: number
): { start: number; end: number; query: string } | null {
  console.log("getMentionInsertPosition called:", { text, cursorPosition });

  // Find the @ symbol before cursor
  let start = cursorPosition - 1;

  while (start >= 0 && text[start] !== "@" && text[start] !== " ") {
    start--;
  }

  console.log("After while loop:", { start, charAtStart: text[start] });

  if (start < 0 || text[start] !== "@") {
    console.log("No @ found, returning null");
    return null;
  }

  // Extract the query after @
  const query = text.slice(start + 1, cursorPosition);

  console.log("Found mention position:", { start, end: cursorPosition, query });

  return {
    start,
    end: cursorPosition,
    query,
  };
}

/**
 * Insert mention into text at specified position
 */
export function insertMention(
  text: string,
  mention: MentionUser,
  insertPosition: { start: number; end: number }
): { text: string; cursorPosition: number } {
  const mentionText = `@${mention.username || mention.name}`;
  const newText =
    text.slice(0, insertPosition.start) +
    mentionText +
    " " +
    text.slice(insertPosition.end);

  const cursorPosition = insertPosition.start + mentionText.length + 1;

  return {
    text: newText,
    cursorPosition,
  };
}
