import React from "react";
import { View, StyleSheet, Text } from "react-native";
import { useLocalSearchParams } from "expo-router";

import { Course } from "@/components/modules/group/Course";
import { Links } from "@/components/modules/group/Links";
import { Events } from "@/components/modules/group/Events";
import { Feed } from "@/components/modules/group/Feed";
import { useMyCohortModule } from "@/lib/api/queries";

export default function ModuleTab() {
  const { groupId, cohortId, moduleId } = useLocalSearchParams<{
    groupId: string;
    cohortId: string;
    moduleId: string;
  }>();

  const { data: moduleData, isLoading } = useMyCohortModule(
    groupId!,
    cohortId!,
    moduleId!
  );

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (!moduleData?.data) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.errorText}>Module not found</Text>
      </View>
    );
  }

  const module = moduleData.data;

  const renderModuleContent = () => {
    switch (module.type) {
      case "course":
        return <Course courseId={module.config.courseId} />;
      case "links":
        return (
          <Links module={module} groupId={groupId!} cohortId={cohortId!} />
        );
      case "events":
        return (
          <Events
            groupId={groupId!}
            cohortId={cohortId!}
            moduleId={module.id}
          />
        );
      case "feed":
        return <Feed module={module} />;
      default:
        return (
          <Text style={styles.errorText}>
            Unsupported module type: {module.type}
          </Text>
        );
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>{renderModuleContent()}</View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#FFFFFF",
    fontSize: 16,
  },
  errorText: {
    color: "#FF6B6B",
    fontSize: 16,
    textAlign: "center",
  },
});
