import React from "react";
import { View, StyleSheet } from "react-native";
import { useLocalSearchParams } from "expo-router";

import { Info } from "@/components/modules/group/Info";
import { useCohort } from "@/lib/api/queries";

export default function InfoTab() {
  const { groupId, cohortId } = useLocalSearchParams<{
    groupId: string;
    cohortId: string;
  }>();

  const { data: cohortData } = useCohort(groupId, cohortId);

  return (
    <View style={styles.container}>
      <Info groupId={groupId!} bio={cohortData?.data?.bio || null} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
});
