import React, { useMemo } from "react";
import { View, StyleSheet } from "react-native";
import { useLocalSearchParams } from "expo-router";

import { Chat } from "@/components/modules/group/Chat";
import { useCohort } from "@/lib/api/queries";

export default function ChatTab() {
  const { groupId, cohortId } = useLocalSearchParams<{
    groupId: string;
    cohortId: string;
  }>();

  const { data: cohortData } = useCohort(groupId, cohortId);

  const discussionModules = useMemo(() => {
    return (
      cohortData?.data?.modules.filter((module) => module.type === "discussion") ?? []
    );
  }, [cohortData?.data?.modules]);

  return (
    <View style={styles.container}>
      <Chat discussionModules={discussionModules} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
    paddingHorizontal: 16,
  },
});
