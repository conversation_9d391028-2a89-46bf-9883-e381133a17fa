import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import { router, useFocusEffect } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { CheckCircle } from "lucide-react-native";

import { useCourse } from "@/lib/api/queries";
import type { Lesson } from "@/lib/api/types";
import { useCallback, useState } from "react";

const ProgressBar = ({ progress }: { progress: number }) => (
  <View style={styles.progressBarContainer}>
    <View style={[styles.progressBar, { width: `${progress}%` }]} />
  </View>
);

const LessonItem: React.FC<{
  lesson: Lesson;
  courseId: number;
  sectionId: number;
}> = ({ lesson, courseId, sectionId }) => {
  const handlePress = () => {
    router.push(
      `/courses/${courseId}/sections/${sectionId}/lessons/${lesson.id}`
    );
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  return (
    <Pressable style={styles.lessonItem} onPress={handlePress}>
      <View style={styles.lessonInfo}>
        <MaterialIcons
          name={
            lesson.lessonContent?.contentType === "video"
              ? "play-circle-outline"
              : "description"
          }
          size={20}
          color="#fff"
        />
        <Text style={styles.lessonTitle}>{lesson.title}</Text>
      </View>
      <View style={styles.lessonRight}>
        {lesson.lessonContent?.contentType === "video" &&
        lesson.lessonContent.metadata?.videoLength ? (
          <Text style={styles.duration}>
            {formatDuration(lesson.lessonContent.metadata.videoLength)}
          </Text>
        ) : null}
        {lesson.isCompleted && (
          <CheckCircle size={20} color="#10B981" style={styles.checkIcon} />
        )}
      </View>
    </Pressable>
  );
};

export const Course: React.FC<{ courseId: number; showProgress?: boolean }> = ({
  courseId,
  showProgress = true,
}) => {
  const { isPending, error, data, refetch } = useCourse(courseId);
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error("Course: Refetch failed:", error);
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  if (isPending) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#fff" />
      </View>
    );
  }

  if (error || !data.success) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={{ color: "#FFF" }}>
          The course is not ready yet. Come back later.
        </Text>
      </View>
    );
  }

  const course = data?.data;
  const isCompleted =
    course.completionPercentage === 100 && course.nextLessonId === 0;

  const handleProgressPress = () => {
    if (!isCompleted && course.nextLessonId > 0) {
      // Find the section containing the next lesson
      const nextSection = course.sections.find((s) =>
        s.lessons.some((l) => l.id === course.nextLessonId)
      );
      if (nextSection) {
        router.push(
          `/courses/${courseId}/sections/${nextSection.id}/lessons/${course.nextLessonId}`
        );
      }
    }
  };

  return (
    <ScrollView
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          tintColor="#fff"
          colors={["#fff"]}
        />
      }
    >
      <View style={styles.container}>
        {showProgress && (
          <Pressable onPress={handleProgressPress}>
            <View style={styles.progressSection}>
              <Text style={styles.continueText}>
                {isCompleted
                  ? "Course Completed"
                  : course.completionPercentage === 0
                  ? "Start Learning"
                  : "Continue Learning"}
              </Text>
              <View style={styles.currentChapter}>
                <Text style={styles.chapterTitle}>
                  {isCompleted
                    ? "Congratulations! You have completed this course."
                    : course.sections
                        .find((s) =>
                          s.lessons.some((l) => l.id === course.nextLessonId)
                        )
                        ?.lessons.find((l) => l.id === course.nextLessonId)
                        ?.title || "Loading..."}
                </Text>
                <Text style={styles.percentageText}>
                  {course.completionPercentage}%
                </Text>
              </View>
              <ProgressBar progress={course.completionPercentage} />
            </View>
          </Pressable>
        )}

        <View style={styles.header}>
          <Text style={styles.title}>{course.name}</Text>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <MaterialIcons name="list" size={16} color="#838A94" />
              <Text style={styles.statText}>
                {course.totalSections || 0} Sections
              </Text>
            </View>
            <View style={styles.statItem}>
              <MaterialIcons name="list" size={16} color="#838A94" />
              <Text style={styles.statText}>
                {course.totalLessons || 0} Lessons
              </Text>
            </View>
          </View>
        </View>

        {course.sections
          .sort((a, b) => a.sectionOrder - b.sectionOrder)
          .map((section) => (
            <View key={section.id} style={styles.section}>
              <Text style={styles.sectionTitle}>{section.name}</Text>
              {section.lessons &&
                section.lessons.map((lesson) => (
                  <LessonItem
                    key={lesson.id}
                    lesson={lesson}
                    courseId={courseId}
                    sectionId={section.id}
                  />
                ))}
            </View>
          ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingVertical: 16,
    paddingTop: 10,
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    color: "#ff4444",
    fontSize: 16,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "700",
    marginBottom: 14,
  },
  statsContainer: {
    flexDirection: "row",
    gap: 14,
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  statText: {
    color: "#838A94",
    fontSize: 12,
    fontWeight: "500",
  },
  section: {
    padding: 16,
    backgroundColor: "rgba(30, 30, 30, 0.3)",
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#1E1E1E",
    marginBottom: 16,
  },
  sectionTitle: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "700",
    marginBottom: 16,
  },
  lessonItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  lessonInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 14,
  },
  lessonTitle: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "500",
  },
  duration: {
    color: "#838A94",
    fontSize: 14,
    fontWeight: "500",
  },
  lessonRight: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  checkIcon: {
    marginLeft: 4,
  },
  progressSection: {
    backgroundColor: "#4F6EF6",
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  continueText: {
    color: "#FFF",
    opacity: 0.7,
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 8,
  },
  currentChapter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    marginBottom: 6,
  },
  chapterTitle: {
    color: "#FFF",
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
    marginRight: 16,
  },
  percentageText: {
    color: "#FFF",
    fontSize: 24,
    fontWeight: "bold",
  },
  progressBarContainer: {
    height: 4,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 2,
    overflow: "hidden",
  },
  progressBar: {
    height: "100%",
    backgroundColor: "#FFCF10",
    borderRadius: 2,
  },
});
